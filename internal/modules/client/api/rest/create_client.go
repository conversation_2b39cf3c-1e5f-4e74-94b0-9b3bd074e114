package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type clientCreate struct {
	Name               string  `json:"name" validate:"required"`
	FatherName         *string `json:"father_name"`
	MotherName         *string `json:"mother_name"`
	ClientType         string  `json:"client_type" validate:"required,oneof=natural juridica"`
	DocumentType       string  `json:"document_type" validate:"required"`
	Document           string  `json:"document" validate:"required"`
	Ubication          *string `json:"ubication"`
	SocialReason       *string `json:"social_reason"`
	CommercialName     *string `json:"commercial_name"`
	Condition          *string `json:"condition"`
	State              *string `json:"state"`
	HasRetentionRegime *bool   `json:"has_retention_regime"`
	BusinessLineID     *string `json:"business_line_id"`
	SubBusinessLineID  *string `json:"sub_business_line_id"`
	ChannelID          *string `json:"channel_id"`
	ContactName        *string `json:"contact_name"`
	Email              *string `json:"email"`
	Phone              *string `json:"phone"`
}

func clientCreateToModel(req clientCreate) model.ClientCreate {
	return model.ClientCreate{
		Name:               req.Name,
		FatherName:         req.FatherName,
		MotherName:         req.MotherName,
		ClientType:         req.ClientType,
		DocumentType:       req.DocumentType,
		Document:           req.Document,
		Ubication:          req.Ubication,
		SocialReason:       req.SocialReason,
		CommercialName:     req.CommercialName,
		Condition:          req.Condition,
		State:              req.State,
		HasRetentionRegime: req.HasRetentionRegime,
		BusinessLineID:     req.BusinessLineID,
		SubBusinessLineID:  req.SubBusinessLineID,
		ChannelID:          req.ChannelID,
		ContactName:        req.ContactName,
		Email:              req.Email,
		Phone:              req.Phone,
	}
}

// Create implements ClientHandler.
func (c *clientHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[clientCreate](w, r, c.validator)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		return
	}

	id, err := c.useCase.Create(ctx, clientCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to create client")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
