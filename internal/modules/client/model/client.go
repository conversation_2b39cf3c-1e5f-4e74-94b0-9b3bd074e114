package model

import "time"

type Client struct {
	ID                   string
	Name                 string
	FatherName           *string
	MotherName           *string
	ClientType           string // "natural" or "juridica"
	DocumentType         string
	Document             string
	Ubication            *string
	SocialReason         *string
	CommercialName       *string
	Condition            *string
	State                *string
	HasRetentionRegime   *bool
	BusinessLineID       *string
	SubBusinessLineID    *string
	ChannelID            *string
	ContactName          *string
	Email                *string
	Phone                *string
	CreatedAt            *time.Time
	UpdatedAt            *time.Time
	DeletedAt            *time.Time
}

type ClientCreate struct {
	Name               string
	FatherName         *string
	MotherName         *string
	ClientType         string // "natural" or "juridica"
	DocumentType       string
	Document           string
	Ubication          *string
	SocialReason       *string
	CommercialName     *string
	Condition          *string
	State              *string
	HasRetentionRegime *bool
	BusinessLineID     *string
	SubBusinessLineID  *string
	ChannelID          *string
	ContactName        *string
	Email              *string
	Phone              *string
}

type ClientUpdate struct {
	ID                 string
	Name               string
	FatherName         *string
	MotherName         *string
	ClientType         string // "natural" or "juridica"
	DocumentType       string
	Document           string
	Ubication          *string
	SocialReason       *string
	CommercialName     *string
	Condition          *string
	State              *string
	HasRetentionRegime *bool
	BusinessLineID     *string
	SubBusinessLineID  *string
	ChannelID          *string
	ContactName        *string
	Email              *string
	Phone              *string
}
