package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/client/model"
)

type clientUsecase struct {
	repo model.ClientRepository
}

func NewClientUsecase(repo model.ClientRepository) model.ClientUsecase {
	return &clientUsecase{
		repo: repo,
	}
}

// Delete implements model.ClientUsecase.
func (c *clientUsecase) Delete(ctx context.Context, id string) error {
	return c.repo.Delete(ctx, id)
}

// GetAll implements model.ClientUsecase.
func (c *clientUsecase) GetAll(ctx context.Context) ([]model.Client, error) {
	return c.repo.GetAll(ctx)
}

// GetByProp implements model.ClientUsecase.
func (c *clientUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Client, error) {
	client, err := c.repo.GetByProp(ctx, prop, value)
	if err != nil {
		return nil, model.ClientNotFoundf("Client not found", err, nil)
	}
	return client, nil
}

// Update implements model.ClientUsecase.
func (c *clientUsecase) Update(ctx context.Context, clientUpdate model.ClientUpdate) error {
	// Check if client exists
	_, err := c.repo.GetByProp(ctx, "id", clientUpdate.ID)
	if err != nil {
		return model.ClientNotFoundf("Client not found", err, nil)
	}

	// Check if name already exists (excluding current client)
	existingClient, err := c.repo.GetByProp(ctx, "name", clientUpdate.Name)
	if err == nil && existingClient.ID != clientUpdate.ID {
		return model.ClientConflictNamef("Client name already exists", nil, nil)
	}

	// Check if document already exists (excluding current client)
	existingClient, err = c.repo.GetByProp(ctx, "document", clientUpdate.Document)
	if err == nil && existingClient.ID != clientUpdate.ID {
		return model.ClientConflictDocumentf("Document already exists", nil, nil)
	}

	client := model.Client{
		ID:                 clientUpdate.ID,
		Name:               clientUpdate.Name,
		FatherName:         clientUpdate.FatherName,
		MotherName:         clientUpdate.MotherName,
		ClientType:         clientUpdate.ClientType,
		DocumentType:       clientUpdate.DocumentType,
		Document:           clientUpdate.Document,
		Ubication:          clientUpdate.Ubication,
		SocialReason:       clientUpdate.SocialReason,
		CommercialName:     clientUpdate.CommercialName,
		Condition:          clientUpdate.Condition,
		State:              clientUpdate.State,
		HasRetentionRegime: clientUpdate.HasRetentionRegime,
		BusinessLineID:     clientUpdate.BusinessLineID,
		SubBusinessLineID:  clientUpdate.SubBusinessLineID,
		ChannelID:          clientUpdate.ChannelID,
		ContactName:        clientUpdate.ContactName,
		Email:              clientUpdate.Email,
		Phone:              clientUpdate.Phone,
	}

	return c.repo.Update(ctx, client)
}

// ValidateDocument implements model.ClientUsecase.
func (c *clientUsecase) ValidateDocument(ctx context.Context, document string) error {
	count, err := c.repo.CountByProp(ctx, "document", document)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.ClientConflictDocumentf("Document already exists", nil, nil)
	}

	return nil
}
